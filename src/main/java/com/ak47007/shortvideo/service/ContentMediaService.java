package com.ak47007.shortvideo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ak47007.shortvideo.entity.ContentMedia;

import java.util.List;

/**
 * Content Media Service
 *
 * <AUTHOR>
 */
public interface ContentMediaService extends IService<ContentMedia> {

    /**
     * 分页查询内容媒体文件
     *
     * @param page      分页参数
     * @param contentId 内容ID（可选）
     * @return 分页结果
     */
    IPage<ContentMedia> pageContentMedia(Page<ContentMedia> page, Long contentId);

    /**
     * 根据内容ID查询媒体文件列表
     *
     * @param contentId 内容ID
     * @return 媒体文件列表
     */
    List<ContentMedia> getByContentId(Long contentId);

    /**
     * 根据内容ID删除媒体文件
     *
     * @param contentId 内容ID
     * @return 是否删除成功
     */
    boolean deleteByContentId(Long contentId);
}
