package com.ak47007.shortvideo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ak47007.shortvideo.common.Result;
import com.ak47007.shortvideo.entity.Content;
import com.ak47007.shortvideo.enums.ContentType;
import com.ak47007.shortvideo.service.ContentService;
import com.ak47007.shortvideo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Content Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/contents")
@Validated
public class ContentController {

    @Autowired
    private ContentService contentService;

    @Autowired
    private UserService userService;

    /**
     * 创建内容
     */
    @PostMapping
    public Result<Content> createContent(@Valid @RequestBody Content content) {
        // 检查用户是否存在
        if (userService.getById(content.getUserId()) == null) {
            return Result.error("用户不存在");
        }

        boolean success = contentService.save(content);
        if (success) {
            return Result.success("内容创建成功", content);
        } else {
            return Result.error("内容创建失败");
        }
    }

    /**
     * 根据ID查询内容
     */
    @GetMapping("/{id}")
    public Result<Content> getContentById(@PathVariable @NotNull Long id) {
        Content content = contentService.getById(id);
        if (content != null) {
            return Result.success(content);
        } else {
            return Result.error("内容不存在");
        }
    }

    /**
     * 分页查询内容
     */
    @GetMapping
    public Result<IPage<Content>> pageContents(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) ContentType contentType) {
        Page<Content> page = new Page<>(current, size);
        IPage<Content> result = contentService.pageContents(page, userId, contentType);
        return Result.success(result);
    }

    /**
     * 查询所有内容
     */
    @GetMapping("/all")
    public Result<List<Content>> getAllContents() {
        List<Content> contents = contentService.list();
        return Result.success(contents);
    }

    /**
     * 根据用户ID查询内容
     */
    @GetMapping("/user/{userId}")
    public Result<List<Content>> getContentsByUserId(@PathVariable @NotNull Long userId) {
        // 检查用户是否存在
        if (userService.getById(userId) == null) {
            return Result.error("用户不存在");
        }

        List<Content> contents = contentService.getByUserId(userId);
        return Result.success(contents);
    }

    /**
     * 根据内容类型查询内容
     */
    @GetMapping("/type/{contentType}")
    public Result<List<Content>> getContentsByType(@PathVariable ContentType contentType) {
        List<Content> contents = contentService.getByContentType(contentType);
        return Result.success(contents);
    }

    /**
     * 更新内容
     */
    @PutMapping("/{id}")
    public Result<Content> updateContent(@PathVariable @NotNull Long id, @Valid @RequestBody Content content) {
        Content existingContent = contentService.getById(id);
        if (existingContent == null) {
            return Result.error("内容不存在");
        }

        // 检查用户是否存在
        if (userService.getById(content.getUserId()) == null) {
            return Result.error("用户不存在");
        }

        content.setId(id);
        boolean success = contentService.updateById(content);
        if (success) {
            return Result.success("内容更新成功", content);
        } else {
            return Result.error("内容更新失败");
        }
    }

    /**
     * 删除内容
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteContent(@PathVariable @NotNull Long id) {
        Content content = contentService.getById(id);
        if (content == null) {
            return Result.error("内容不存在");
        }

        boolean success = contentService.removeById(id);
        if (success) {
            return Result.success("内容删除成功");
        } else {
            return Result.error("内容删除失败");
        }
    }
}
