package com.ak47007.shortvideo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ak47007.shortvideo.entity.User;

/**
 * User Service
 *
 * <AUTHOR>
 */
public interface UserService extends IService<User> {

    /**
     * 分页查询用户
     *
     * @param page     分页参数
     * @param username 用户名（可选）
     * @return 分页结果
     */
    IPage<User> pageUsers(Page<User> page, String username);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User getByUsername(String username);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
}
