{"info": {"name": "短视频服务 API", "description": "Short Video Service API Collection", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "contentId", "value": "", "type": "string"}, {"key": "mediaId", "value": "", "type": "string"}, {"key": "commentId", "value": "", "type": "string"}], "item": [{"name": "用户管理", "item": [{"name": "创建用户", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"avatarUrl\": \"https://example.com/avatar.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}, "response": []}, {"name": "根据ID查询用户", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}, "response": []}, {"name": "根据用户名查询用户", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/username/testuser", "host": ["{{baseUrl}}"], "path": ["api", "users", "username", "testuser"]}}, "response": []}, {"name": "分页查询用户", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?current=1&size=10&username=test", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}, {"key": "username", "value": "test"}]}}, "response": []}, {"name": "查询所有用户", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/all", "host": ["{{baseUrl}}"], "path": ["api", "users", "all"]}}, "response": []}, {"name": "更新用户", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"updateduser\",\n  \"avatarUrl\": \"https://example.com/updated-avatar.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}, "response": []}, {"name": "删除用户", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}, "response": []}]}, {"name": "内容管理", "item": [{"name": "创建内容", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": {{userId}},\n  \"description\": \"这是一个测试内容\",\n  \"contentType\": \"image\",\n  \"coverImg\": \"https://example.com/cover.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/contents", "host": ["{{baseUrl}}"], "path": ["api", "contents"]}}, "response": []}, {"name": "根据ID查询内容", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contents/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "contents", "{{contentId}}"]}}, "response": []}, {"name": "分页查询内容", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contents?current=1&size=10&userId={{userId}}&contentType=image", "host": ["{{baseUrl}}"], "path": ["api", "contents"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}, {"key": "userId", "value": "{{userId}}"}, {"key": "contentType", "value": "image"}]}}, "response": []}, {"name": "根据用户ID查询内容", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contents/user/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "contents", "user", "{{userId}}"]}}, "response": []}, {"name": "根据类型查询内容", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contents/type/image", "host": ["{{baseUrl}}"], "path": ["api", "contents", "type", "image"]}}, "response": []}, {"name": "查询所有内容", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contents/all", "host": ["{{baseUrl}}"], "path": ["api", "contents", "all"]}}, "response": []}, {"name": "更新内容", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": {{userId}},\n  \"description\": \"更新后的内容描述\",\n  \"contentType\": \"video\",\n  \"coverImg\": \"https://example.com/updated-cover.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/api/contents/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "contents", "{{contentId}}"]}}, "response": []}, {"name": "删除内容", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/contents/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "contents", "{{contentId}}"]}}, "response": []}]}, {"name": "媒体文件管理", "item": [{"name": "创建媒体文件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": {{contentId}},\n  \"mediaUrl\": \"https://example.com/media.jpg\",\n  \"mediaOrder\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/content-media", "host": ["{{baseUrl}}"], "path": ["api", "content-media"]}}, "response": []}, {"name": "根据ID查询媒体文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media/{{mediaId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "{{mediaId}}"]}}, "response": []}, {"name": "分页查询媒体文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media?current=1&size=10&contentId={{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}, {"key": "contentId", "value": "{{contentId}}"}]}}, "response": []}, {"name": "根据内容ID查询媒体文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media/content/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "content", "{{contentId}}"]}}, "response": []}, {"name": "查询所有媒体文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media/all", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "all"]}}, "response": []}, {"name": "更新媒体文件", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": {{contentId}},\n  \"mediaUrl\": \"https://example.com/updated-media.jpg\",\n  \"mediaOrder\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/content-media/{{mediaId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "{{mediaId}}"]}}, "response": []}, {"name": "删除媒体文件", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media/{{mediaId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "{{mediaId}}"]}}, "response": []}, {"name": "根据内容ID删除所有媒体文件", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/content-media/content/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "content-media", "content", "{{contentId}}"]}}, "response": []}]}, {"name": "评论管理", "item": [{"name": "创建评论", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": {{contentId}},\n  \"userId\": {{userId}},\n  \"parentId\": null,\n  \"commentText\": \"这是一个评论\"\n}"}, "url": {"raw": "{{baseUrl}}/api/comments", "host": ["{{baseUrl}}"], "path": ["api", "comments"]}}, "response": []}, {"name": "创建二级评论", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": {{contentId}},\n  \"userId\": {{userId}},\n  \"parentId\": {{commentId}},\n  \"commentText\": \"这是一个回复评论\"\n}"}, "url": {"raw": "{{baseUrl}}/api/comments", "host": ["{{baseUrl}}"], "path": ["api", "comments"]}}, "response": []}, {"name": "根据ID查询评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/{{commentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "{{commentId}}"]}}, "response": []}, {"name": "分页查询评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments?current=1&size=10&contentId={{contentId}}&userId={{userId}}&parentId=null", "host": ["{{baseUrl}}"], "path": ["api", "comments"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}, {"key": "contentId", "value": "{{contentId}}"}, {"key": "userId", "value": "{{userId}}"}, {"key": "parentId", "value": "null"}]}}, "response": []}, {"name": "根据内容ID查询评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/content/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "content", "{{contentId}}"]}}, "response": []}, {"name": "根据用户ID查询评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/user/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "user", "{{userId}}"]}}, "response": []}, {"name": "根据父评论ID查询子评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/parent/{{commentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "parent", "{{commentId}}"]}}, "response": []}, {"name": "查询所有评论", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/all", "host": ["{{baseUrl}}"], "path": ["api", "comments", "all"]}}, "response": []}, {"name": "更新评论", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": {{contentId}},\n  \"userId\": {{userId}},\n  \"parentId\": null,\n  \"commentText\": \"更新后的评论内容\"\n}"}, "url": {"raw": "{{baseUrl}}/api/comments/{{commentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "{{commentId}}"]}}, "response": []}, {"name": "删除评论", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/{{commentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "{{commentId}}"]}}, "response": []}, {"name": "根据内容ID删除所有评论", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/comments/content/{{contentId}}", "host": ["{{baseUrl}}"], "path": ["api", "comments", "content", "{{contentId}}"]}}, "response": []}]}]}