package com.ak47007.shortvideo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ak47007.shortvideo.entity.Content;
import com.ak47007.shortvideo.enums.ContentType;
import com.ak47007.shortvideo.mapper.ContentMapper;
import com.ak47007.shortvideo.service.ContentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Content Service Implementation
 *
 * <AUTHOR>
 */
@Service
public class ContentServiceImpl extends ServiceImpl<ContentMapper, Content> implements ContentService {

    @Override
    public IPage<Content> pageContents(Page<Content> page, Long userId, ContentType contentType) {
        LambdaQueryWrapper<Content> queryWrapper = new LambdaQueryWrapper<>();
        if (userId != null) {
            queryWrapper.eq(Content::getUserId, userId);
        }
        if (contentType != null) {
            queryWrapper.eq(Content::getContentType, contentType);
        }
        queryWrapper.orderByDesc(Content::getCreatedAt);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<Content> getByUserId(Long userId) {
        LambdaQueryWrapper<Content> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Content::getUserId, userId);
        queryWrapper.orderByDesc(Content::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<Content> getByContentType(ContentType contentType) {
        LambdaQueryWrapper<Content> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Content::getContentType, contentType);
        queryWrapper.orderByDesc(Content::getCreatedAt);
        return this.list(queryWrapper);
    }
}
