package com.ak47007.shortvideo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ak47007.shortvideo.entity.Content;
import com.ak47007.shortvideo.enums.ContentType;

import java.util.List;

/**
 * Content Service
 *
 * <AUTHOR>
 */
public interface ContentService extends IService<Content> {

    /**
     * 分页查询内容
     *
     * @param page        分页参数
     * @param userId      用户ID（可选）
     * @param contentType 内容类型（可选）
     * @return 分页结果
     */
    IPage<Content> pageContents(Page<Content> page, Long userId, ContentType contentType);

    /**
     * 根据用户ID查询内容列表
     *
     * @param userId 用户ID
     * @return 内容列表
     */
    List<Content> getByUserId(Long userId);

    /**
     * 根据内容类型查询内容列表
     *
     * @param contentType 内容类型
     * @return 内容列表
     */
    List<Content> getByContentType(ContentType contentType);
}
