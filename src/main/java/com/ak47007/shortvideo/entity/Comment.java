package com.ak47007.shortvideo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Comment Entity
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comments")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评论ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 内容ID（逻辑外键）
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 评论用户ID（逻辑外键）
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 父评论ID（逻辑外键，用于二级评论）
     */
    private Long parentId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    private String commentText;

    /**
     * 评论时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
