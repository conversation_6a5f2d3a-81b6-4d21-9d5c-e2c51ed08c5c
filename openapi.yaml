openapi: 3.0.3
info:
  title: 短视频服务 API
  description: |
    短视频服务后端API接口文档
    
    ## 功能特性
    - 用户管理：用户注册、查询、更新、删除
    - 内容管理：内容发布、查询、更新、删除
    - 媒体文件管理：媒体文件上传、查询、更新、删除
    - 评论管理：评论发布、查询、更新、删除，支持二级评论
    
    ## 技术栈
    - Java 8 + Spring Boot 2.7.5
    - MySQL 8 + MyBatis Plus
    - 雪花算法ID生成
    - Hutool + Lombok
  version: 1.0.0
  contact:
    name: ShortVideo Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: 开发环境
  - url: https://api.shortvideo.com
    description: 生产环境

tags:
  - name: users
    description: 用户管理
  - name: contents
    description: 内容管理
  - name: content-media
    description: 媒体文件管理
  - name: comments
    description: 评论管理

components:
  schemas:
    Result:
      type: object
      properties:
        code:
          type: integer
          description: 状态码，200表示成功
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          description: 响应数据
          oneOf:
            - type: object
            - type: array
            - type: string
            - type: "null"
      required:
        - code
        - message

    PageResult:
      type: object
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "操作成功"
        data:
          type: object
          properties:
            records:
              type: array
              description: 当前页数据列表
            total:
              type: integer
              description: 总记录数
              example: 100
            size:
              type: integer
              description: 每页大小
              example: 10
            current:
              type: integer
              description: 当前页码
              example: 1
            pages:
              type: integer
              description: 总页数
              example: 10

    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 用户ID（雪花ID）
          example: 1234567890123456789
        username:
          type: string
          description: 用户名
          maxLength: 50
          example: "testuser"
        avatarUrl:
          type: string
          description: 用户头像URL
          maxLength: 500
          example: "https://example.com/avatar.jpg"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2023-12-01T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2023-12-01T10:30:00"
      required:
        - username
        - avatarUrl

    UserCreate:
      type: object
      properties:
        username:
          type: string
          description: 用户名
          maxLength: 50
          example: "testuser"
        avatarUrl:
          type: string
          description: 用户头像URL
          maxLength: 500
          example: "https://example.com/avatar.jpg"
      required:
        - username
        - avatarUrl

    Content:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 内容ID（雪花ID）
          example: 1234567890123456790
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1234567890123456789
        description:
          type: string
          description: 内容描述
          example: "这是一个测试内容"
        contentType:
          type: string
          enum: [image, video]
          description: 内容类型
          example: "image"
        createdAt:
          type: string
          format: date-time
          description: 发布时间
          example: "2023-12-01T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2023-12-01T10:30:00"
      required:
        - userId
        - description
        - contentType

    ContentCreate:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1234567890123456789
        description:
          type: string
          description: 内容描述
          example: "这是一个测试内容"
        contentType:
          type: string
          enum: [image, video]
          description: 内容类型
          example: "image"
      required:
        - userId
        - description
        - contentType

    ContentMedia:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 媒体文件ID（雪花ID）
          example: 1234567890123456791
        contentId:
          type: integer
          format: int64
          description: 内容ID
          example: 1234567890123456790
        mediaUrl:
          type: string
          description: 媒体文件URL
          maxLength: 500
          example: "https://example.com/media.jpg"
        mediaOrder:
          type: integer
          description: 媒体文件顺序
          example: 1
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2023-12-01T10:30:00"
      required:
        - contentId
        - mediaUrl

    ContentMediaCreate:
      type: object
      properties:
        contentId:
          type: integer
          format: int64
          description: 内容ID
          example: 1234567890123456790
        mediaUrl:
          type: string
          description: 媒体文件URL
          maxLength: 500
          example: "https://example.com/media.jpg"
        mediaOrder:
          type: integer
          description: 媒体文件顺序
          example: 1
      required:
        - contentId
        - mediaUrl

    Comment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 评论ID（雪花ID）
          example: 1234567890123456793
        contentId:
          type: integer
          format: int64
          description: 内容ID
          example: 1234567890123456790
        userId:
          type: integer
          format: int64
          description: 评论用户ID
          example: 1234567890123456789
        parentId:
          type: integer
          format: int64
          nullable: true
          description: 父评论ID（用于二级评论）
          example: null
        commentText:
          type: string
          description: 评论内容
          example: "这是一个评论"
        createdAt:
          type: string
          format: date-time
          description: 评论时间
          example: "2023-12-01T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2023-12-01T10:30:00"
      required:
        - contentId
        - userId
        - commentText

    CommentCreate:
      type: object
      properties:
        contentId:
          type: integer
          format: int64
          description: 内容ID
          example: 1234567890123456790
        userId:
          type: integer
          format: int64
          description: 评论用户ID
          example: 1234567890123456789
        parentId:
          type: integer
          format: int64
          nullable: true
          description: 父评论ID（用于二级评论）
          example: null
        commentText:
          type: string
          description: 评论内容
          example: "这是一个评论"
      required:
        - contentId
        - userId
        - commentText

    Error:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        message:
          type: string
          description: 错误信息
          example: "操作失败"
        data:
          type: "null"
          example: null

  parameters:
    PageCurrent:
      name: current
      in: query
      description: 当前页码
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1
        example: 1

    PageSize:
      name: size
      in: query
      description: 每页大小
      required: false
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
        example: 10

    UserId:
      name: id
      in: path
      description: 用户ID
      required: true
      schema:
        type: integer
        format: int64
        example: 1234567890123456789

    ContentId:
      name: id
      in: path
      description: 内容ID
      required: true
      schema:
        type: integer
        format: int64
        example: 1234567890123456790

    MediaId:
      name: id
      in: path
      description: 媒体文件ID
      required: true
      schema:
        type: integer
        format: int64
        example: 1234567890123456791

    CommentId:
      name: id
      in: path
      description: 评论ID
      required: true
      schema:
        type: integer
        format: int64
        example: 1234567890123456793

paths:
  /api/users:
    post:
      tags:
        - users
      summary: 创建用户
      description: 创建新用户，用户名必须唯一
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
      responses:
        '200':
          description: 用户创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '500':
          description: 创建失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                username_exists:
                  summary: 用户名已存在
                  value:
                    code: 500
                    message: "用户名已存在"
                    data: null

    get:
      tags:
        - users
      summary: 分页查询用户
      description: 分页查询用户列表，支持用户名模糊搜索
      parameters:
        - $ref: '#/components/parameters/PageCurrent'
        - $ref: '#/components/parameters/PageSize'
        - name: username
          in: query
          description: 用户名模糊查询
          required: false
          schema:
            type: string
            example: "test"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PageResult'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          records:
                            type: array
                            items:
                              $ref: '#/components/schemas/User'

  /api/users/{id}:
    get:
      tags:
        - users
      summary: 根据ID查询用户
      description: 根据用户ID查询用户详细信息
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '500':
          description: 用户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: 500
                message: "用户不存在"
                data: null

    put:
      tags:
        - users
      summary: 更新用户
      description: 根据用户ID更新用户信息
      parameters:
        - $ref: '#/components/parameters/UserId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '500':
          description: 更新失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - users
      summary: 删除用户
      description: 根据用户ID删除用户
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        type: "null"
        '500':
          description: 删除失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/users/username/{username}:
    get:
      tags:
        - users
      summary: 根据用户名查询用户
      description: 根据用户名查询用户详细信息
      parameters:
        - name: username
          in: path
          description: 用户名
          required: true
          schema:
            type: string
            example: "testuser"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '500':
          description: 用户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/users/all:
    get:
      tags:
        - users
      summary: 查询所有用户
      description: 查询所有用户列表
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/Result'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
