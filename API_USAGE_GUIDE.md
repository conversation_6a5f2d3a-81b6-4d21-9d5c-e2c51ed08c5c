# 短视频服务 API 使用指南

## 📖 概述

本指南将帮助您快速上手短视频服务的API接口，包含完整的使用示例和最佳实践。

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境已安装：
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+

### 2. 启动服务

```bash
# 克隆项目
git clone <repository-url>
cd ShortVideoService

# 配置数据库
# 修改 src/main/resources/application.yml 中的数据库连接信息

# 启动服务
mvn clean install
mvn spring-boot:run
```

服务启动后，默认运行在 `http://localhost:8080`

### 3. 验证服务

```bash
curl http://localhost:8080/api/users/all
```

如果返回JSON格式的响应，说明服务启动成功。

## 📋 API 文档资源

我们提供了多种格式的API文档：

1. **详细文档**: `API_DOCUMENTATION.md` - 包含所有接口的详细说明
2. **Postman集合**: `postman_collection.json` - 可直接导入Postman进行测试
3. **OpenAPI规范**: `openapi.yaml` - 标准的OpenAPI 3.0规范文件
4. **测试脚本**: `test_api.sh` - 自动化测试脚本

## 🔧 使用工具

### Postman 使用

1. 打开Postman
2. 点击 `Import` 按钮
3. 选择 `postman_collection.json` 文件
4. 导入后即可看到所有API接口
5. 设置环境变量 `baseUrl` 为 `http://localhost:8080`

### cURL 使用

所有示例都提供了cURL命令，可以直接在终端中执行。

### 自动化测试

```bash
# 给脚本执行权限
chmod +x test_api.sh

# 运行测试
./test_api.sh
```

## 💡 使用示例

### 完整业务流程

以下是一个完整的业务流程示例，展示如何使用API创建用户、发布内容、上传媒体文件和添加评论。

#### 步骤1: 创建用户

```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "alice",
    "avatarUrl": "https://example.com/alice-avatar.jpg"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "用户创建成功",
  "data": {
    "id": 1734567890123456789,
    "username": "alice",
    "avatarUrl": "https://example.com/alice-avatar.jpg",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T10:30:00"
  }
}
```

#### 步骤2: 发布内容

```bash
curl -X POST http://localhost:8080/api/contents \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1734567890123456789,
    "description": "我的第一个短视频作品！",
    "contentType": "video"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "内容创建成功",
  "data": {
    "id": 1734567890123456790,
    "userId": 1734567890123456789,
    "description": "我的第一个短视频作品！",
    "contentType": "video",
    "createdAt": "2023-12-01T10:35:00",
    "updatedAt": "2023-12-01T10:35:00"
  }
}
```

#### 步骤3: 上传媒体文件

```bash
curl -X POST http://localhost:8080/api/content-media \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1734567890123456790,
    "mediaUrl": "https://cdn.example.com/videos/alice-video-1.mp4",
    "mediaOrder": 1
  }'
```

#### 步骤4: 添加评论

```bash
curl -X POST http://localhost:8080/api/comments \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1734567890123456790,
    "userId": 1734567890123456789,
    "commentText": "这个视频拍得真不错！"
  }'
```

#### 步骤5: 回复评论

```bash
curl -X POST http://localhost:8080/api/comments \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1734567890123456790,
    "userId": 1734567890123456789,
    "parentId": 1734567890123456793,
    "commentText": "谢谢夸奖！"
  }'
```

### 查询操作示例

#### 分页查询内容

```bash
# 查询第1页，每页10条记录
curl "http://localhost:8080/api/contents?current=1&size=10"

# 查询特定用户的内容
curl "http://localhost:8080/api/contents?userId=1734567890123456789"

# 查询特定类型的内容
curl "http://localhost:8080/api/contents?contentType=video"

# 组合查询
curl "http://localhost:8080/api/contents?current=1&size=5&userId=1734567890123456789&contentType=video"
```

#### 查询用户信息

```bash
# 根据ID查询
curl "http://localhost:8080/api/users/1734567890123456789"

# 根据用户名查询
curl "http://localhost:8080/api/users/username/alice"

# 分页查询用户
curl "http://localhost:8080/api/users?current=1&size=10&username=alice"
```

#### 查询评论

```bash
# 查询内容的所有评论
curl "http://localhost:8080/api/comments/content/1734567890123456790"

# 查询用户的所有评论
curl "http://localhost:8080/api/comments/user/1734567890123456789"

# 查询评论的回复
curl "http://localhost:8080/api/comments/parent/1734567890123456793"
```

## 🎯 最佳实践

### 1. 错误处理

始终检查响应中的 `code` 字段：

```javascript
// JavaScript 示例
fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    avatarUrl: 'https://example.com/avatar.jpg'
  })
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('成功:', data.data);
  } else {
    console.error('错误:', data.message);
  }
});
```

### 2. 分页查询

使用分页查询时，注意处理分页信息：

```javascript
// 处理分页响应
const handlePageResponse = (response) => {
  const { records, total, current, size, pages } = response.data;
  
  console.log(`当前第${current}页，共${pages}页`);
  console.log(`每页${size}条，总共${total}条记录`);
  console.log('数据:', records);
};
```

### 3. ID 管理

由于使用雪花算法生成ID，ID是64位长整型：

```javascript
// 在JavaScript中处理长整型ID
const userId = "1734567890123456789"; // 使用字符串避免精度丢失

// 在URL中使用
const url = `/api/users/${userId}`;
```

### 4. 内容类型

内容类型枚举值：
- `image`: 图片内容
- `video`: 视频内容

```json
{
  "contentType": "video"  // 或 "image"
}
```

### 5. 二级评论

创建二级评论时，需要指定 `parentId`：

```json
{
  "contentId": 1734567890123456790,
  "userId": 1734567890123456789,
  "parentId": 1734567890123456793,  // 父评论ID
  "commentText": "回复内容"
}
```

## 🔍 调试技巧

### 1. 查看日志

服务启动时会输出详细的SQL日志，帮助调试：

```yaml
# application.yml
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

### 2. 使用测试脚本

运行自动化测试脚本来验证所有接口：

```bash
./test_api.sh
```

### 3. 检查数据库

直接查询数据库验证数据：

```sql
-- 查看用户表
SELECT * FROM users ORDER BY created_at DESC LIMIT 10;

-- 查看内容表
SELECT * FROM contents ORDER BY created_at DESC LIMIT 10;

-- 查看评论表
SELECT * FROM comments ORDER BY created_at DESC LIMIT 10;
```

## 🚨 常见问题

### 1. 用户名已存在

**错误**: `{"code": 500, "message": "用户名已存在"}`

**解决**: 使用不同的用户名或先查询用户名是否存在

### 2. 用户不存在

**错误**: `{"code": 500, "message": "用户不存在"}`

**解决**: 确认用户ID正确，或先创建用户

### 3. 内容不存在

**错误**: `{"code": 500, "message": "内容不存在"}`

**解决**: 确认内容ID正确，或先创建内容

### 4. 参数验证失败

**错误**: 参数验证相关错误信息

**解决**: 检查请求参数是否符合要求：
- 用户名：1-50个字符，不能为空
- 头像URL：1-500个字符，不能为空
- 内容描述：不能为空
- 媒体文件URL：1-500个字符，不能为空

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看详细的API文档：`API_DOCUMENTATION.md`
2. 运行测试脚本验证环境：`./test_api.sh`
3. 检查服务日志输出
4. 确认数据库连接和表结构正确

## 🔄 版本更新

当前版本：v1.0.0

更新日志请查看项目的 CHANGELOG.md 文件。
