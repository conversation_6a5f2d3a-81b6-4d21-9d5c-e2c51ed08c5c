# 短视频服务 API 接口总结

## 📊 接口概览

| 模块 | 接口数量 | 功能描述 |
|------|----------|----------|
| 用户管理 | 7个 | 用户的增删改查操作 |
| 内容管理 | 8个 | 内容的增删改查操作 |
| 媒体文件管理 | 8个 | 媒体文件的增删改查操作 |
| 评论管理 | 10个 | 评论的增删改查操作，支持二级评论 |
| **总计** | **33个** | 完整的CRUD功能 |

## 🔗 接口列表

### 用户管理接口 (7个)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/api/users` | 创建用户 | 用户名必须唯一 |
| GET | `/api/users/{id}` | 根据ID查询用户 | 返回用户详细信息 |
| GET | `/api/users/username/{username}` | 根据用户名查询用户 | 精确匹配用户名 |
| GET | `/api/users` | 分页查询用户 | 支持用户名模糊搜索 |
| GET | `/api/users/all` | 查询所有用户 | 返回所有用户列表 |
| PUT | `/api/users/{id}` | 更新用户 | 更新用户信息 |
| DELETE | `/api/users/{id}` | 删除用户 | 物理删除用户 |

### 内容管理接口 (8个)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/api/contents` | 创建内容 | 需要验证用户存在 |
| GET | `/api/contents/{id}` | 根据ID查询内容 | 返回内容详细信息 |
| GET | `/api/contents` | 分页查询内容 | 支持用户ID和类型筛选 |
| GET | `/api/contents/all` | 查询所有内容 | 返回所有内容列表 |
| GET | `/api/contents/user/{userId}` | 根据用户ID查询内容 | 查询指定用户的所有内容 |
| GET | `/api/contents/type/{contentType}` | 根据类型查询内容 | 查询指定类型的所有内容 |
| PUT | `/api/contents/{id}` | 更新内容 | 更新内容信息 |
| DELETE | `/api/contents/{id}` | 删除内容 | 物理删除内容 |

### 媒体文件管理接口 (8个)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/api/content-media` | 创建媒体文件 | 需要验证内容存在 |
| GET | `/api/content-media/{id}` | 根据ID查询媒体文件 | 返回媒体文件详细信息 |
| GET | `/api/content-media` | 分页查询媒体文件 | 支持内容ID筛选 |
| GET | `/api/content-media/all` | 查询所有媒体文件 | 返回所有媒体文件列表 |
| GET | `/api/content-media/content/{contentId}` | 根据内容ID查询媒体文件 | 查询指定内容的所有媒体文件 |
| PUT | `/api/content-media/{id}` | 更新媒体文件 | 更新媒体文件信息 |
| DELETE | `/api/content-media/{id}` | 删除媒体文件 | 物理删除媒体文件 |
| DELETE | `/api/content-media/content/{contentId}` | 批量删除媒体文件 | 删除指定内容的所有媒体文件 |

### 评论管理接口 (10个)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/api/comments` | 创建评论 | 支持一级和二级评论 |
| GET | `/api/comments/{id}` | 根据ID查询评论 | 返回评论详细信息 |
| GET | `/api/comments` | 分页查询评论 | 支持多维度筛选 |
| GET | `/api/comments/all` | 查询所有评论 | 返回所有评论列表 |
| GET | `/api/comments/content/{contentId}` | 根据内容ID查询评论 | 查询指定内容的所有评论 |
| GET | `/api/comments/user/{userId}` | 根据用户ID查询评论 | 查询指定用户的所有评论 |
| GET | `/api/comments/parent/{parentId}` | 根据父评论ID查询子评论 | 查询指定评论的所有回复 |
| PUT | `/api/comments/{id}` | 更新评论 | 更新评论信息 |
| DELETE | `/api/comments/{id}` | 删除评论 | 物理删除评论 |
| DELETE | `/api/comments/content/{contentId}` | 批量删除评论 | 删除指定内容的所有评论 |

## 🎯 核心特性

### 1. 雪花算法ID生成
- 所有实体使用雪花算法生成唯一ID
- 保证分布式环境下ID的唯一性
- ID为64位长整型

### 2. 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 3. 分页查询支持
- 统一的分页参数：`current`（当前页）、`size`（每页大小）
- 标准的分页响应格式
- 默认按创建时间倒序排列

### 4. 参数验证
- 使用Bean Validation进行参数校验
- 详细的错误信息提示
- 支持自定义验证规则

### 5. 关联关系验证
- 创建时验证关联实体是否存在
- 防止数据不一致问题
- 提供友好的错误提示

### 6. 二级评论支持
- 支持评论的回复功能
- 通过`parentId`字段实现层级关系
- 可以查询指定评论的所有回复

## 📈 数据流转

```mermaid
graph TD
    A[用户注册] --> B[创建内容]
    B --> C[上传媒体文件]
    C --> D[发布评论]
    D --> E[回复评论]
    
    F[查询用户] --> G[查询用户内容]
    G --> H[查询内容媒体]
    H --> I[查询内容评论]
    I --> J[查询评论回复]
```

## 🔧 技术实现

### 后端技术栈
- **框架**: Spring Boot 2.7.5
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.2
- **工具库**: Hutool 5.8.10, Lombok
- **验证**: Bean Validation
- **ID生成**: 雪花算法

### 数据库设计
- **用户表** (users): 存储用户基本信息
- **内容表** (contents): 存储内容信息
- **媒体文件表** (content_media): 存储媒体文件信息
- **评论表** (comments): 存储评论信息，支持层级结构

### 架构模式
- **分层架构**: Controller -> Service -> Mapper -> Database
- **RESTful API**: 遵循REST设计原则
- **统一异常处理**: 全局异常处理机制
- **参数验证**: 统一的参数验证机制

## 📋 使用场景

### 1. 短视频应用
- 用户注册和管理
- 视频内容发布
- 视频文件管理
- 用户互动评论

### 2. 图片分享应用
- 用户管理
- 图片内容发布
- 图片文件管理
- 用户评论互动

### 3. 内容管理系统
- 多媒体内容管理
- 用户权限管理
- 内容审核和管理
- 用户反馈管理

## 🚀 性能特点

### 1. 高效查询
- 使用MyBatis Plus提供的高效查询方法
- 支持条件构造器进行复杂查询
- 自动分页处理

### 2. 数据库优化
- 合理的索引设计
- 雪花算法避免ID冲突
- 时间字段自动管理

### 3. 内存优化
- 使用Lombok减少样板代码
- 合理的对象设计
- 避免N+1查询问题

## 📚 文档资源

1. **详细API文档**: `API_DOCUMENTATION.md`
   - 包含所有接口的详细说明
   - 请求和响应示例
   - 错误码说明

2. **使用指南**: `API_USAGE_GUIDE.md`
   - 快速上手指南
   - 完整业务流程示例
   - 最佳实践和调试技巧

3. **Postman集合**: `postman_collection.json`
   - 可直接导入Postman
   - 包含所有接口的测试用例
   - 预设环境变量

4. **OpenAPI规范**: `openapi.yaml`
   - 标准的OpenAPI 3.0规范
   - 可用于生成客户端SDK
   - 支持Swagger UI展示

5. **自动化测试**: `test_api.sh`
   - 完整的API测试脚本
   - 自动验证所有接口功能
   - 彩色输出和详细日志

## 🔮 扩展建议

### 1. 安全增强
- 添加JWT认证
- 实现权限控制
- 添加API限流

### 2. 功能扩展
- 添加点赞功能
- 实现关注/粉丝系统
- 添加内容推荐算法

### 3. 性能优化
- 添加Redis缓存
- 实现读写分离
- 添加CDN支持

### 4. 监控运维
- 添加健康检查接口
- 实现日志收集
- 添加性能监控

## 📞 支持信息

- **项目版本**: v1.0.0
- **最后更新**: 2023-12-01
- **技术支持**: 查看项目README.md
- **问题反馈**: 通过GitHub Issues提交

---

*本文档提供了短视频服务API的完整概览，更多详细信息请参考相关文档文件。*
