package com.ak47007.shortvideo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ak47007.shortvideo.enums.ContentType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Content Entity
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("contents")
public class Content implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内容ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID（逻辑外键）
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 内容描述
     */
    @NotBlank(message = "内容描述不能为空")
    private String description;

    /**
     * 内容类型：图片/视频
     */
    @NotNull(message = "内容类型不能为空")
    private ContentType contentType;

    /**
     * 发布时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
