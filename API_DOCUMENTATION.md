# 短视频服务 API 接口文档

## 📋 目录
- [接口概述](#接口概述)
- [通用规范](#通用规范)
- [用户管理接口](#用户管理接口)
- [内容管理接口](#内容管理接口)
- [媒体文件管理接口](#媒体文件管理接口)
- [评论管理接口](#评论管理接口)
- [错误码说明](#错误码说明)

## 接口概述

### 基础信息
- **服务名称**: 短视频服务 (Short Video Service)
- **版本**: v1.0.0
- **基础URL**: `http://localhost:8080`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 技术栈
- **后端框架**: Spring Boot 2.7.5
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.2
- **ID生成**: 雪花算法 (Snowflake)
- **工具库**: Hutool, Lombok

## 通用规范

### 请求头
```http
Content-Type: application/json
Accept: application/json
```

### 统一响应格式
所有接口均返回以下格式的JSON数据：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

**响应字段说明**:
- `code`: 状态码，200表示成功，其他表示失败
- `message`: 响应消息，描述操作结果
- `data`: 响应数据，成功时包含具体数据，失败时为null

### 分页响应格式
分页查询接口返回格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

**分页字段说明**:
- `records`: 当前页数据列表
- `total`: 总记录数
- `size`: 每页大小
- `current`: 当前页码
- `pages`: 总页数

### 通用查询参数
- `current`: 当前页码，默认值为1
- `size`: 每页大小，默认值为10

## 用户管理接口

### 1. 创建用户

**接口地址**: `POST /api/users`

**请求参数**:
```json
{
  "username": "testuser",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 长度限制 | 说明 |
|--------|------|------|----------|------|
| username | String | 是 | 1-50 | 用户名，全局唯一 |
| avatarUrl | String | 是 | 1-500 | 用户头像URL |

**成功响应**:
```json
{
  "code": 200,
  "message": "用户创建成功",
  "data": {
    "id": 1234567890123456789,
    "username": "testuser",
    "avatarUrl": "https://example.com/avatar.jpg",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T10:30:00"
  }
}
```

**错误响应**:
```json
{
  "code": 500,
  "message": "用户名已存在",
  "data": null
}
```

### 2. 根据ID查询用户

**接口地址**: `GET /api/users/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 用户ID |

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1234567890123456789,
    "username": "testuser",
    "avatarUrl": "https://example.com/avatar.jpg",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T10:30:00"
  }
}
```

### 3. 根据用户名查询用户

**接口地址**: `GET /api/users/username/{username}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | String | 是 | 用户名 |

### 4. 分页查询用户

**接口地址**: `GET /api/users`

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| username | String | 否 | - | 用户名模糊查询 |

**请求示例**:
```http
GET /api/users?current=1&size=10&username=test
```

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1234567890123456789,
        "username": "testuser",
        "avatarUrl": "https://example.com/avatar.jpg",
        "createdAt": "2023-12-01T10:30:00",
        "updatedAt": "2023-12-01T10:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 5. 查询所有用户

**接口地址**: `GET /api/users/all`

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1234567890123456789,
      "username": "testuser",
      "avatarUrl": "https://example.com/avatar.jpg",
      "createdAt": "2023-12-01T10:30:00",
      "updatedAt": "2023-12-01T10:30:00"
    }
  ]
}
```

### 6. 更新用户

**接口地址**: `PUT /api/users/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 用户ID |

**请求参数**:
```json
{
  "username": "newusername",
  "avatarUrl": "https://example.com/new-avatar.jpg"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "用户更新成功",
  "data": {
    "id": 1234567890123456789,
    "username": "newusername",
    "avatarUrl": "https://example.com/new-avatar.jpg",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T11:30:00"
  }
}
```

### 7. 删除用户

**接口地址**: `DELETE /api/users/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 用户ID |

**成功响应**:
```json
{
  "code": 200,
  "message": "用户删除成功",
  "data": null
}
```

## 内容管理接口

### 1. 创建内容

**接口地址**: `POST /api/contents`

**请求参数**:
```json
{
  "userId": 1234567890123456789,
  "description": "这是一个测试内容",
  "contentType": "image"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| description | String | 是 | 内容描述 |
| contentType | String | 是 | 内容类型：image/video |

**成功响应**:
```json
{
  "code": 200,
  "message": "内容创建成功",
  "data": {
    "id": 1234567890123456790,
    "userId": 1234567890123456789,
    "description": "这是一个测试内容",
    "contentType": "image",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T10:30:00"
  }
}
```

### 2. 根据ID查询内容

**接口地址**: `GET /api/contents/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 内容ID |

### 3. 分页查询内容

**接口地址**: `GET /api/contents`

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| userId | Long | 否 | - | 用户ID筛选 |
| contentType | String | 否 | - | 内容类型筛选 |

**请求示例**:
```http
GET /api/contents?current=1&size=10&userId=1234567890123456789&contentType=image
```

### 4. 根据用户ID查询内容

**接口地址**: `GET /api/contents/user/{userId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

### 5. 根据内容类型查询内容

**接口地址**: `GET /api/contents/type/{contentType}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentType | String | 是 | 内容类型：image/video |

### 6. 查询所有内容

**接口地址**: `GET /api/contents/all`

### 7. 更新内容

**接口地址**: `PUT /api/contents/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 内容ID |

**请求参数**:
```json
{
  "userId": 1234567890123456789,
  "description": "更新后的内容描述",
  "contentType": "video"
}
```

### 8. 删除内容

**接口地址**: `DELETE /api/contents/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 内容ID |

## 媒体文件管理接口

### 1. 创建媒体文件

**接口地址**: `POST /api/content-media`

**请求参数**:
```json
{
  "contentId": 1234567890123456790,
  "mediaUrl": "https://example.com/media.jpg",
  "mediaOrder": 1
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 长度限制 | 说明 |
|--------|------|------|----------|------|
| contentId | Long | 是 | - | 内容ID |
| mediaUrl | String | 是 | 1-500 | 媒体文件URL |
| mediaOrder | Integer | 否 | - | 媒体文件顺序，默认为0 |

**成功响应**:
```json
{
  "code": 200,
  "message": "媒体文件创建成功",
  "data": {
    "id": 1234567890123456791,
    "contentId": 1234567890123456790,
    "mediaUrl": "https://example.com/media.jpg",
    "mediaOrder": 1,
    "createdAt": "2023-12-01T10:30:00"
  }
}
```

### 2. 根据ID查询媒体文件

**接口地址**: `GET /api/content-media/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 媒体文件ID |

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1234567890123456791,
    "contentId": 1234567890123456790,
    "mediaUrl": "https://example.com/media.jpg",
    "mediaOrder": 1,
    "createdAt": "2023-12-01T10:30:00"
  }
}
```

### 3. 分页查询媒体文件

**接口地址**: `GET /api/content-media`

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| contentId | Long | 否 | - | 内容ID筛选 |

**请求示例**:
```http
GET /api/content-media?current=1&size=10&contentId=1234567890123456790
```

### 4. 根据内容ID查询媒体文件

**接口地址**: `GET /api/content-media/content/{contentId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentId | Long | 是 | 内容ID |

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1234567890123456791,
      "contentId": 1234567890123456790,
      "mediaUrl": "https://example.com/media1.jpg",
      "mediaOrder": 1,
      "createdAt": "2023-12-01T10:30:00"
    },
    {
      "id": 1234567890123456792,
      "contentId": 1234567890123456790,
      "mediaUrl": "https://example.com/media2.jpg",
      "mediaOrder": 2,
      "createdAt": "2023-12-01T10:31:00"
    }
  ]
}
```

### 5. 查询所有媒体文件

**接口地址**: `GET /api/content-media/all`

### 6. 更新媒体文件

**接口地址**: `PUT /api/content-media/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 媒体文件ID |

**请求参数**:
```json
{
  "contentId": 1234567890123456790,
  "mediaUrl": "https://example.com/updated-media.jpg",
  "mediaOrder": 2
}
```

### 7. 删除媒体文件

**接口地址**: `DELETE /api/content-media/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 媒体文件ID |

### 8. 根据内容ID删除所有媒体文件

**接口地址**: `DELETE /api/content-media/content/{contentId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentId | Long | 是 | 内容ID |

**成功响应**:
```json
{
  "code": 200,
  "message": "媒体文件删除成功",
  "data": null
}
```

## 评论管理接口

### 1. 创建评论

**接口地址**: `POST /api/comments`

**请求参数**:
```json
{
  "contentId": 1234567890123456790,
  "userId": 1234567890123456789,
  "parentId": null,
  "commentText": "这是一个评论"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentId | Long | 是 | 内容ID |
| userId | Long | 是 | 评论用户ID |
| parentId | Long | 否 | 父评论ID，用于二级评论 |
| commentText | String | 是 | 评论内容 |

**成功响应**:
```json
{
  "code": 200,
  "message": "评论创建成功",
  "data": {
    "id": 1234567890123456793,
    "contentId": 1234567890123456790,
    "userId": 1234567890123456789,
    "parentId": null,
    "commentText": "这是一个评论",
    "createdAt": "2023-12-01T10:30:00",
    "updatedAt": "2023-12-01T10:30:00"
  }
}
```

**二级评论示例**:
```json
{
  "contentId": 1234567890123456790,
  "userId": 1234567890123456789,
  "parentId": 1234567890123456793,
  "commentText": "这是一个回复评论"
}
```

### 2. 根据ID查询评论

**接口地址**: `GET /api/comments/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评论ID |

### 3. 分页查询评论

**接口地址**: `GET /api/comments`

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| contentId | Long | 否 | - | 内容ID筛选 |
| userId | Long | 否 | - | 用户ID筛选 |
| parentId | Long | 否 | - | 父评论ID筛选 |

**请求示例**:
```http
GET /api/comments?current=1&size=10&contentId=1234567890123456790&parentId=null
```

### 4. 根据内容ID查询评论

**接口地址**: `GET /api/comments/content/{contentId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentId | Long | 是 | 内容ID |

### 5. 根据用户ID查询评论

**接口地址**: `GET /api/comments/user/{userId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

### 6. 根据父评论ID查询子评论

**接口地址**: `GET /api/comments/parent/{parentId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parentId | Long | 是 | 父评论ID |

### 7. 查询所有评论

**接口地址**: `GET /api/comments/all`

### 8. 更新评论

**接口地址**: `PUT /api/comments/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评论ID |

**请求参数**:
```json
{
  "contentId": 1234567890123456790,
  "userId": 1234567890123456789,
  "parentId": null,
  "commentText": "更新后的评论内容"
}
```

### 9. 删除评论

**接口地址**: `DELETE /api/comments/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评论ID |

### 10. 根据内容ID删除所有评论

**接口地址**: `DELETE /api/comments/content/{contentId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contentId | Long | 是 | 内容ID |

## 错误码说明

### HTTP状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务错误码
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 操作成功 | 请求处理成功 |
| 500 | 操作失败 | 通用错误 |
| 500 | 用户名已存在 | 创建用户时用户名重复 |
| 500 | 用户不存在 | 查询或操作的用户不存在 |
| 500 | 内容不存在 | 查询或操作的内容不存在 |
| 500 | 媒体文件不存在 | 查询或操作的媒体文件不存在 |
| 500 | 评论不存在 | 查询或操作的评论不存在 |
| 500 | 父评论不存在 | 创建二级评论时父评论不存在 |

### 参数验证错误
| 错误信息 | 说明 |
|----------|------|
| 用户名不能为空 | username字段为空 |
| 用户名长度不能超过50个字符 | username字段长度超限 |
| 头像URL不能为空 | avatarUrl字段为空 |
| 头像URL长度不能超过500个字符 | avatarUrl字段长度超限 |
| 内容描述不能为空 | description字段为空 |
| 内容类型不能为空 | contentType字段为空 |
| 媒体文件URL不能为空 | mediaUrl字段为空 |
| 媒体文件URL长度不能超过500个字符 | mediaUrl字段长度超限 |
| 评论内容不能为空 | commentText字段为空 |
| 用户ID不能为空 | userId字段为空 |
| 内容ID不能为空 | contentId字段为空 |

## 使用示例

### 完整业务流程示例

#### 1. 创建用户
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "avatarUrl": "https://example.com/avatar.jpg"
  }'
```

#### 2. 创建内容
```bash
curl -X POST http://localhost:8080/api/contents \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1234567890123456789,
    "description": "我的第一个短视频",
    "contentType": "video"
  }'
```

#### 3. 上传媒体文件
```bash
curl -X POST http://localhost:8080/api/content-media \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1234567890123456790,
    "mediaUrl": "https://example.com/video.mp4",
    "mediaOrder": 1
  }'
```

#### 4. 添加评论
```bash
curl -X POST http://localhost:8080/api/comments \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1234567890123456790,
    "userId": 1234567890123456789,
    "commentText": "很棒的视频！"
  }'
```

#### 5. 回复评论
```bash
curl -X POST http://localhost:8080/api/comments \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1234567890123456790,
    "userId": 1234567890123456789,
    "parentId": 1234567890123456793,
    "commentText": "谢谢支持！"
  }'
```

### 查询示例

#### 分页查询内容
```bash
curl "http://localhost:8080/api/contents?current=1&size=5&contentType=video"
```

#### 查询用户的所有内容
```bash
curl "http://localhost:8080/api/contents/user/1234567890123456789"
```

#### 查询内容的所有评论
```bash
curl "http://localhost:8080/api/comments/content/1234567890123456790"
```

#### 查询评论的所有回复
```bash
curl "http://localhost:8080/api/comments/parent/1234567890123456793"
```

## 注意事项

1. **ID生成**: 所有实体的ID均使用雪花算法生成，确保全局唯一
2. **时间格式**: 所有时间字段均使用ISO 8601格式：`yyyy-MM-ddTHH:mm:ss`
3. **字符编码**: 请求和响应均使用UTF-8编码
4. **参数验证**: 所有必填参数都会进行验证，不符合要求会返回相应错误信息
5. **关联检查**: 创建或更新时会检查关联实体是否存在
6. **分页查询**: 默认按创建时间倒序排列
7. **媒体文件**: 按mediaOrder字段升序排列
8. **二级评论**: 支持一级回复，parentId为null表示一级评论

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2023-12-01 | 初始版本，包含用户、内容、媒体文件、评论的完整CRUD功能 |
