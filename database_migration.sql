-- 短视频服务数据库迁移脚本
-- 版本: v1.1.0
-- 更新日期: 2023-12-01
-- 更新内容: 为内容表添加封面字段

-- 使用数据库
USE short_video;

-- 为内容表添加封面字段
ALTER TABLE contents 
ADD COLUMN cover_img VARCHAR(500) NULL COMMENT '封面图片URL' 
AFTER content_type;

-- 验证字段是否添加成功
DESCRIBE contents;

-- 查看表结构
SHOW CREATE TABLE contents;

-- 可选：为现有数据添加默认封面（如果需要）
-- UPDATE contents SET cover_img = 'https://example.com/default-cover.jpg' WHERE cover_img IS NULL;

-- 创建索引（可选，如果需要按封面查询）
-- CREATE INDEX idx_cover_img ON contents(cover_img);

-- 迁移完成提示
SELECT 'Contents table migration completed successfully!' as status;
