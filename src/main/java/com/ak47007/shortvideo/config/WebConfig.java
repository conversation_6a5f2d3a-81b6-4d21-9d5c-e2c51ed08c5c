package com.ak47007.shortvideo.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @Date 2023/3/7 17:14
 * @Description:
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 页面跨域访问Controller过滤
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 设置允许跨域的路径
        CorsRegistration corsRegistration = registry.addMapping("/**");
        // 设置允许跨域请求的域名
        corsRegistration.allowedHeaders("*");
        // 设置允许的header属性
        corsRegistration.allowedOriginPatterns("*");
        // 设置允许的方法
        corsRegistration.allowedMethods("POST", "GET", "OPTION","PUT");
        // 允许跨域Cookie
        corsRegistration.allowCredentials(true);
    }

}
