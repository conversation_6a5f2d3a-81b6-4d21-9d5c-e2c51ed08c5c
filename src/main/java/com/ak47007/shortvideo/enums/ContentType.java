package com.ak47007.shortvideo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * Content Type Enum
 *
 * <AUTHOR>
 */
@Getter
public enum ContentType {
    
    IMAGE("image", "图片"),
    VIDEO("video", "视频");
    
    @EnumValue
    @JsonValue
    private final String value;
    private final String description;
    
    ContentType(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
