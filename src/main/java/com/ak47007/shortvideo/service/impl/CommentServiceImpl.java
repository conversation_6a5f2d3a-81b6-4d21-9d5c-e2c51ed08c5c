package com.ak47007.shortvideo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ak47007.shortvideo.entity.Comment;
import com.ak47007.shortvideo.mapper.CommentMapper;
import com.ak47007.shortvideo.service.CommentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Comment Service Implementation
 *
 * <AUTHOR>
 */
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    @Override
    public IPage<Comment> pageComments(Page<Comment> page, Long contentId, Long userId, Long parentId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        if (contentId != null) {
            queryWrapper.eq(Comment::getContentId, contentId);
        }
        if (userId != null) {
            queryWrapper.eq(Comment::getUserId, userId);
        }
        if (parentId != null) {
            queryWrapper.eq(Comment::getParentId, parentId);
        }
        queryWrapper.orderByDesc(Comment::getCreatedAt);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<Comment> getByContentId(Long contentId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getContentId, contentId);
        queryWrapper.orderByDesc(Comment::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<Comment> getByUserId(Long userId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getUserId, userId);
        queryWrapper.orderByDesc(Comment::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<Comment> getByParentId(Long parentId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getParentId, parentId);
        queryWrapper.orderByDesc(Comment::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteByContentId(Long contentId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Comment::getContentId, contentId);
        return this.remove(queryWrapper);
    }
}
