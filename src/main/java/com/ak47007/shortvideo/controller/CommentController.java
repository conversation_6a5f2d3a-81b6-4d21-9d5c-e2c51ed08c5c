package com.ak47007.shortvideo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ak47007.shortvideo.common.Result;
import com.ak47007.shortvideo.entity.Comment;
import com.ak47007.shortvideo.service.CommentService;
import com.ak47007.shortvideo.service.ContentService;
import com.ak47007.shortvideo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Comment Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/comments")
@Validated
public class CommentController {

    @Autowired
    private CommentService commentService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private UserService userService;

    /**
     * 创建评论
     */
    @PostMapping
    public Result<Comment> createComment(@Valid @RequestBody Comment comment) {
        // 检查内容是否存在
        if (contentService.getById(comment.getContentId()) == null) {
            return Result.error("内容不存在");
        }

        // 检查用户是否存在
        if (userService.getById(comment.getUserId()) == null) {
            return Result.error("用户不存在");
        }

        // 如果是二级评论，检查父评论是否存在
        if (comment.getParentId() != null && commentService.getById(comment.getParentId()) == null) {
            return Result.error("父评论不存在");
        }

        boolean success = commentService.save(comment);
        if (success) {
            return Result.success("评论创建成功", comment);
        } else {
            return Result.error("评论创建失败");
        }
    }

    /**
     * 根据ID查询评论
     */
    @GetMapping("/{id}")
    public Result<Comment> getCommentById(@PathVariable @NotNull Long id) {
        Comment comment = commentService.getById(id);
        if (comment != null) {
            return Result.success(comment);
        } else {
            return Result.error("评论不存在");
        }
    }

    /**
     * 分页查询评论
     */
    @GetMapping
    public Result<IPage<Comment>> pageComments(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long contentId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long parentId) {
        Page<Comment> page = new Page<>(current, size);
        IPage<Comment> result = commentService.pageComments(page, contentId, userId, parentId);
        return Result.success(result);
    }

    /**
     * 查询所有评论
     */
    @GetMapping("/all")
    public Result<List<Comment>> getAllComments() {
        List<Comment> comments = commentService.list();
        return Result.success(comments);
    }

    /**
     * 根据内容ID查询评论
     */
    @GetMapping("/content/{contentId}")
    public Result<List<Comment>> getCommentsByContentId(@PathVariable @NotNull Long contentId) {
        // 检查内容是否存在
        if (contentService.getById(contentId) == null) {
            return Result.error("内容不存在");
        }

        List<Comment> comments = commentService.getByContentId(contentId);
        return Result.success(comments);
    }

    /**
     * 根据用户ID查询评论
     */
    @GetMapping("/user/{userId}")
    public Result<List<Comment>> getCommentsByUserId(@PathVariable @NotNull Long userId) {
        // 检查用户是否存在
        if (userService.getById(userId) == null) {
            return Result.error("用户不存在");
        }

        List<Comment> comments = commentService.getByUserId(userId);
        return Result.success(comments);
    }

    /**
     * 根据父评论ID查询子评论
     */
    @GetMapping("/parent/{parentId}")
    public Result<List<Comment>> getCommentsByParentId(@PathVariable @NotNull Long parentId) {
        // 检查父评论是否存在
        if (commentService.getById(parentId) == null) {
            return Result.error("父评论不存在");
        }

        List<Comment> comments = commentService.getByParentId(parentId);
        return Result.success(comments);
    }

    /**
     * 更新评论
     */
    @PutMapping("/{id}")
    public Result<Comment> updateComment(@PathVariable @NotNull Long id, @Valid @RequestBody Comment comment) {
        Comment existingComment = commentService.getById(id);
        if (existingComment == null) {
            return Result.error("评论不存在");
        }

        // 检查内容是否存在
        if (contentService.getById(comment.getContentId()) == null) {
            return Result.error("内容不存在");
        }

        // 检查用户是否存在
        if (userService.getById(comment.getUserId()) == null) {
            return Result.error("用户不存在");
        }

        // 如果是二级评论，检查父评论是否存在
        if (comment.getParentId() != null && commentService.getById(comment.getParentId()) == null) {
            return Result.error("父评论不存在");
        }

        comment.setId(id);
        boolean success = commentService.updateById(comment);
        if (success) {
            return Result.success("评论更新成功", comment);
        } else {
            return Result.error("评论更新失败");
        }
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteComment(@PathVariable @NotNull Long id) {
        Comment comment = commentService.getById(id);
        if (comment == null) {
            return Result.error("评论不存在");
        }

        boolean success = commentService.removeById(id);
        if (success) {
            return Result.success("评论删除成功");
        } else {
            return Result.error("评论删除失败");
        }
    }

    /**
     * 根据内容ID删除所有评论
     */
    @DeleteMapping("/content/{contentId}")
    public Result<Void> deleteCommentsByContentId(@PathVariable @NotNull Long contentId) {
        // 检查内容是否存在
        if (contentService.getById(contentId) == null) {
            return Result.error("内容不存在");
        }

        boolean success = commentService.deleteByContentId(contentId);
        if (success) {
            return Result.success("评论删除成功");
        } else {
            return Result.error("评论删除失败");
        }
    }
}
