package com.ak47007.shortvideo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Content Media Entity
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("content_media")
public class ContentMedia implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 媒体文件ID（雪花ID）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 内容ID（逻辑外键）
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 媒体文件URL
     */
    @NotBlank(message = "媒体文件URL不能为空")
    @Size(max = 500, message = "媒体文件URL长度不能超过500个字符")
    private String mediaUrl;

    /**
     * 媒体文件顺序
     */
    private Integer mediaOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
