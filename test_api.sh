#!/bin/bash

# 短视频服务 API 测试脚本
# 使用方法: ./test_api.sh

BASE_URL="http://localhost:8080"
CONTENT_TYPE="Content-Type: application/json"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# 检查服务是否启动
check_service() {
    print_header "检查服务状态"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/users/all")
    
    if [ "$response" = "200" ]; then
        print_success "服务正常运行"
        return 0
    else
        print_error "服务未启动或无法访问 (HTTP $response)"
        print_info "请确保服务已启动并运行在 $BASE_URL"
        exit 1
    fi
}

# 测试用户管理接口
test_user_apis() {
    print_header "测试用户管理接口"
    
    # 1. 创建用户
    print_info "1. 创建用户"
    user_response=$(curl -s -X POST "$BASE_URL/api/users" \
        -H "$CONTENT_TYPE" \
        -d '{
            "username": "testuser_'$(date +%s)'",
            "avatarUrl": "https://example.com/avatar.jpg"
        }')
    
    user_id=$(echo "$user_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$user_id" ]; then
        print_success "用户创建成功，ID: $user_id"
    else
        print_error "用户创建失败"
        echo "$user_response"
        return 1
    fi
    
    # 2. 根据ID查询用户
    print_info "2. 根据ID查询用户"
    get_user_response=$(curl -s "$BASE_URL/api/users/$user_id")
    
    if echo "$get_user_response" | grep -q '"code":200'; then
        print_success "用户查询成功"
    else
        print_error "用户查询失败"
        echo "$get_user_response"
    fi
    
    # 3. 分页查询用户
    print_info "3. 分页查询用户"
    page_users_response=$(curl -s "$BASE_URL/api/users?current=1&size=5")
    
    if echo "$page_users_response" | grep -q '"code":200'; then
        print_success "分页查询用户成功"
    else
        print_error "分页查询用户失败"
        echo "$page_users_response"
    fi
    
    # 4. 更新用户
    print_info "4. 更新用户"
    update_user_response=$(curl -s -X PUT "$BASE_URL/api/users/$user_id" \
        -H "$CONTENT_TYPE" \
        -d '{
            "username": "updated_testuser_'$(date +%s)'",
            "avatarUrl": "https://example.com/updated-avatar.jpg"
        }')
    
    if echo "$update_user_response" | grep -q '"code":200'; then
        print_success "用户更新成功"
    else
        print_error "用户更新失败"
        echo "$update_user_response"
    fi
    
    # 返回用户ID供后续测试使用
    echo "$user_id"
}

# 测试内容管理接口
test_content_apis() {
    local user_id=$1
    print_header "测试内容管理接口"
    
    # 1. 创建内容
    print_info "1. 创建内容"
    content_response=$(curl -s -X POST "$BASE_URL/api/contents" \
        -H "$CONTENT_TYPE" \
        -d '{
            "userId": '$user_id',
            "description": "这是一个测试内容",
            "contentType": "image",
            "coverImg": "https://example.com/test-cover.jpg"
        }')
    
    content_id=$(echo "$content_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$content_id" ]; then
        print_success "内容创建成功，ID: $content_id"
    else
        print_error "内容创建失败"
        echo "$content_response"
        return 1
    fi
    
    # 2. 根据ID查询内容
    print_info "2. 根据ID查询内容"
    get_content_response=$(curl -s "$BASE_URL/api/contents/$content_id")
    
    if echo "$get_content_response" | grep -q '"code":200'; then
        print_success "内容查询成功"
    else
        print_error "内容查询失败"
        echo "$get_content_response"
    fi
    
    # 3. 根据用户ID查询内容
    print_info "3. 根据用户ID查询内容"
    user_contents_response=$(curl -s "$BASE_URL/api/contents/user/$user_id")
    
    if echo "$user_contents_response" | grep -q '"code":200'; then
        print_success "根据用户ID查询内容成功"
    else
        print_error "根据用户ID查询内容失败"
        echo "$user_contents_response"
    fi
    
    # 4. 根据类型查询内容
    print_info "4. 根据类型查询内容"
    type_contents_response=$(curl -s "$BASE_URL/api/contents/type/image")
    
    if echo "$type_contents_response" | grep -q '"code":200'; then
        print_success "根据类型查询内容成功"
    else
        print_error "根据类型查询内容失败"
        echo "$type_contents_response"
    fi
    
    # 返回内容ID供后续测试使用
    echo "$content_id"
}

# 测试媒体文件管理接口
test_media_apis() {
    local content_id=$1
    print_header "测试媒体文件管理接口"
    
    # 1. 创建媒体文件
    print_info "1. 创建媒体文件"
    media_response=$(curl -s -X POST "$BASE_URL/api/content-media" \
        -H "$CONTENT_TYPE" \
        -d '{
            "contentId": '$content_id',
            "mediaUrl": "https://example.com/media.jpg",
            "mediaOrder": 1
        }')
    
    media_id=$(echo "$media_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$media_id" ]; then
        print_success "媒体文件创建成功，ID: $media_id"
    else
        print_error "媒体文件创建失败"
        echo "$media_response"
        return 1
    fi
    
    # 2. 根据ID查询媒体文件
    print_info "2. 根据ID查询媒体文件"
    get_media_response=$(curl -s "$BASE_URL/api/content-media/$media_id")
    
    if echo "$get_media_response" | grep -q '"code":200'; then
        print_success "媒体文件查询成功"
    else
        print_error "媒体文件查询失败"
        echo "$get_media_response"
    fi
    
    # 3. 根据内容ID查询媒体文件
    print_info "3. 根据内容ID查询媒体文件"
    content_media_response=$(curl -s "$BASE_URL/api/content-media/content/$content_id")
    
    if echo "$content_media_response" | grep -q '"code":200'; then
        print_success "根据内容ID查询媒体文件成功"
    else
        print_error "根据内容ID查询媒体文件失败"
        echo "$content_media_response"
    fi
    
    # 返回媒体文件ID供后续测试使用
    echo "$media_id"
}

# 测试评论管理接口
test_comment_apis() {
    local content_id=$1
    local user_id=$2
    print_header "测试评论管理接口"
    
    # 1. 创建评论
    print_info "1. 创建评论"
    comment_response=$(curl -s -X POST "$BASE_URL/api/comments" \
        -H "$CONTENT_TYPE" \
        -d '{
            "contentId": '$content_id',
            "userId": '$user_id',
            "parentId": null,
            "commentText": "这是一个测试评论"
        }')
    
    comment_id=$(echo "$comment_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$comment_id" ]; then
        print_success "评论创建成功，ID: $comment_id"
    else
        print_error "评论创建失败"
        echo "$comment_response"
        return 1
    fi
    
    # 2. 创建二级评论
    print_info "2. 创建二级评论"
    reply_response=$(curl -s -X POST "$BASE_URL/api/comments" \
        -H "$CONTENT_TYPE" \
        -d '{
            "contentId": '$content_id',
            "userId": '$user_id',
            "parentId": '$comment_id',
            "commentText": "这是一个回复评论"
        }')
    
    reply_id=$(echo "$reply_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$reply_id" ]; then
        print_success "二级评论创建成功，ID: $reply_id"
    else
        print_error "二级评论创建失败"
        echo "$reply_response"
    fi
    
    # 3. 根据内容ID查询评论
    print_info "3. 根据内容ID查询评论"
    content_comments_response=$(curl -s "$BASE_URL/api/comments/content/$content_id")
    
    if echo "$content_comments_response" | grep -q '"code":200'; then
        print_success "根据内容ID查询评论成功"
    else
        print_error "根据内容ID查询评论失败"
        echo "$content_comments_response"
    fi
    
    # 4. 根据父评论ID查询子评论
    print_info "4. 根据父评论ID查询子评论"
    parent_comments_response=$(curl -s "$BASE_URL/api/comments/parent/$comment_id")
    
    if echo "$parent_comments_response" | grep -q '"code":200'; then
        print_success "根据父评论ID查询子评论成功"
    else
        print_error "根据父评论ID查询子评论失败"
        echo "$parent_comments_response"
    fi
}

# 主函数
main() {
    print_header "短视频服务 API 测试"
    
    # 检查服务状态
    check_service
    
    # 测试用户管理接口
    user_id=$(test_user_apis)
    
    if [ -n "$user_id" ]; then
        # 测试内容管理接口
        content_id=$(test_content_apis "$user_id")
        
        if [ -n "$content_id" ]; then
            # 测试媒体文件管理接口
            media_id=$(test_media_apis "$content_id")
            
            # 测试评论管理接口
            test_comment_apis "$content_id" "$user_id"
        fi
    fi
    
    print_header "测试完成"
    print_success "所有API接口测试完成！"
    print_info "详细的API文档请查看 API_DOCUMENTATION.md"
    print_info "Postman集合文件: postman_collection.json"
    print_info "OpenAPI规范文件: openapi.yaml"
}

# 执行主函数
main
