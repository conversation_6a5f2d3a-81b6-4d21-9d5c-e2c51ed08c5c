# Short Video Service

基于 Java 8 + Spring Boot 2.7.5 + MySQL 8 + MyBatis Plus + Lombok + Hutool 的短视频服务后端项目。

## 技术栈

- **Java**: 1.8
- **Spring Boot**: 2.7.5
- **数据库**: MySQL 8
- **ORM**: MyBatis Plus 3.5.2
- **工具库**: Hutool 5.8.10, Lombok
- **ID生成**: 雪花算法 (Snowflake)

## 项目结构

```
src/
├── main/
│   ├── java/com/shortvideo/
│   │   ├── ShortVideoServiceApplication.java    # 启动类
│   │   ├── config/
│   │   │   └── MyBatisPlusConfig.java          # MyBatis Plus配置
│   │   ├── common/
│   │   │   └── Result.java                     # 统一响应结果
│   │   ├── enums/
│   │   │   └── ContentType.java                # 内容类型枚举
│   │   ├── entity/                             # 实体类
│   │   │   ├── User.java                       # 用户实体
│   │   │   ├── Content.java                    # 内容实体
│   │   │   ├── ContentMedia.java               # 内容媒体实体
│   │   │   └── Comment.java                    # 评论实体
│   │   ├── mapper/                             # Mapper接口
│   │   │   ├── UserMapper.java
│   │   │   ├── ContentMapper.java
│   │   │   ├── ContentMediaMapper.java
│   │   │   └── CommentMapper.java
│   │   ├── service/                            # 服务层
│   │   │   ├── UserService.java
│   │   │   ├── ContentService.java
│   │   │   ├── ContentMediaService.java
│   │   │   ├── CommentService.java
│   │   │   └── impl/                           # 服务实现
│   │   └── controller/                         # 控制器层
│   │       ├── UserController.java
│   │       ├── ContentController.java
│   │       ├── ContentMediaController.java
│   │       └── CommentController.java
│   └── resources/
│       └── application.yml                     # 应用配置
└── test/                                       # 测试代码
```

## 数据库表结构

### 用户表 (users)
- `id`: 用户ID（雪花ID）
- `username`: 用户名（唯一）
- `avatar_url`: 头像URL
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 内容表 (contents)
- `id`: 内容ID（雪花ID）
- `user_id`: 用户ID（逻辑外键）
- `description`: 内容描述
- `content_type`: 内容类型（image/video）
- `created_at`: 发布时间
- `updated_at`: 更新时间

### 内容媒体文件表 (content_media)
- `id`: 媒体文件ID（雪花ID）
- `content_id`: 内容ID（逻辑外键）
- `media_url`: 媒体文件URL
- `media_order`: 媒体文件顺序
- `created_at`: 创建时间

### 评论表 (comments)
- `id`: 评论ID（雪花ID）
- `content_id`: 内容ID（逻辑外键）
- `user_id`: 评论用户ID（逻辑外键）
- `parent_id`: 父评论ID（用于二级评论）
- `comment_text`: 评论内容
- `created_at`: 评论时间
- `updated_at`: 更新时间

## 快速开始

### 1. 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置
1. 创建数据库 `short_video`
2. 执行 `prompt.txt` 中的 SQL 脚本创建表结构
3. 修改 `application.yml` 中的数据库连接配置

### 3. 运行项目
```bash
mvn clean install
mvn spring-boot:run
```

项目启动后访问: http://localhost:8080

## API 接口文档

### 用户管理 (/api/users)

#### 创建用户
- **POST** `/api/users`
- **请求体**:
```json
{
  "username": "testuser",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

#### 查询用户
- **GET** `/api/users/{id}` - 根据ID查询
- **GET** `/api/users/username/{username}` - 根据用户名查询
- **GET** `/api/users` - 分页查询
  - 参数: `current`, `size`, `username`
- **GET** `/api/users/all` - 查询所有用户

#### 更新用户
- **PUT** `/api/users/{id}`

#### 删除用户
- **DELETE** `/api/users/{id}`

### 内容管理 (/api/contents)

#### 创建内容
- **POST** `/api/contents`
- **请求体**:
```json
{
  "userId": 1234567890,
  "description": "这是一个测试内容",
  "contentType": "image"
}
```

#### 查询内容
- **GET** `/api/contents/{id}` - 根据ID查询
- **GET** `/api/contents` - 分页查询
  - 参数: `current`, `size`, `userId`, `contentType`
- **GET** `/api/contents/user/{userId}` - 根据用户ID查询
- **GET** `/api/contents/type/{contentType}` - 根据类型查询
- **GET** `/api/contents/all` - 查询所有内容

#### 更新内容
- **PUT** `/api/contents/{id}`

#### 删除内容
- **DELETE** `/api/contents/{id}`

### 媒体文件管理 (/api/content-media)

#### 创建媒体文件
- **POST** `/api/content-media`
- **请求体**:
```json
{
  "contentId": 1234567890,
  "mediaUrl": "https://example.com/media.jpg",
  "mediaOrder": 1
}
```

#### 查询媒体文件
- **GET** `/api/content-media/{id}` - 根据ID查询
- **GET** `/api/content-media` - 分页查询
  - 参数: `current`, `size`, `contentId`
- **GET** `/api/content-media/content/{contentId}` - 根据内容ID查询
- **GET** `/api/content-media/all` - 查询所有媒体文件

#### 更新媒体文件
- **PUT** `/api/content-media/{id}`

#### 删除媒体文件
- **DELETE** `/api/content-media/{id}`
- **DELETE** `/api/content-media/content/{contentId}` - 删除内容的所有媒体文件

### 评论管理 (/api/comments)

#### 创建评论
- **POST** `/api/comments`
- **请求体**:
```json
{
  "contentId": 1234567890,
  "userId": 1234567890,
  "parentId": null,
  "commentText": "这是一个评论"
}
```

#### 查询评论
- **GET** `/api/comments/{id}` - 根据ID查询
- **GET** `/api/comments` - 分页查询
  - 参数: `current`, `size`, `contentId`, `userId`, `parentId`
- **GET** `/api/comments/content/{contentId}` - 根据内容ID查询
- **GET** `/api/comments/user/{userId}` - 根据用户ID查询
- **GET** `/api/comments/parent/{parentId}` - 根据父评论ID查询
- **GET** `/api/comments/all` - 查询所有评论

#### 更新评论
- **PUT** `/api/comments/{id}`

#### 删除评论
- **DELETE** `/api/comments/{id}`
- **DELETE** `/api/comments/content/{contentId}` - 删除内容的所有评论

## 响应格式

所有API接口统一返回格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 特性

- ✅ 使用雪花算法生成唯一ID
- ✅ 支持分页查询
- ✅ 参数校验
- ✅ 统一异常处理
- ✅ 逻辑外键关联检查
- ✅ 支持二级评论
- ✅ RESTful API设计
