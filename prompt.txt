java8 springboot 2.17.5 mysql8 mybatis plus lombok hutool

完成这些表的增删改查操作，id使用雪花id
-- auto-generated definition
CREATE TABLE comments
(
    id           BIGINT                              NOT NULL COMMENT '评论ID（雪花ID）'
        PRIMARY KEY,
    content_id   BIGINT                              NOT NULL COMMENT '内容ID（逻辑外键）',
    user_id      BIGINT                              NOT NULL COMMENT '评论用户ID（逻辑外键）',
    parent_id    BIGINT                              NULL COMMENT '父评论ID（逻辑外键，用于二级评论）',
    comment_text TEXT                                NOT NULL COMMENT '评论内容',
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL COMMENT '评论时间',
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
)
    COMMENT '评论表';

CREATE TABLE content_media
(
    id          BIGINT                              NOT NULL COMMENT '媒体文件ID（雪花ID）'
        PRIMARY KEY,
    content_id  BIGINT                              NOT NULL COMMENT '内容ID（逻辑外键）',
    media_url   VARCHAR(500)                        NOT NULL COMMENT '媒体文件URL',
    media_order INT       DEFAULT 0                 NULL COMMENT '媒体文件顺序',
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL COMMENT '创建时间'
)
    COMMENT '内容媒体文件表';

    CREATE TABLE contents
    (
        id           BIGINT                              NOT NULL COMMENT '内容ID（雪花ID）'
            PRIMARY KEY,
        user_id      BIGINT                              NOT NULL COMMENT '用户ID（逻辑外键）',
        description  TEXT                                NOT NULL COMMENT '内容描述',
        content_type ENUM ('image', 'video')             NOT NULL COMMENT '内容类型：图片/视频',
        created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL COMMENT '发布时间',
        updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    )
        COMMENT '内容表';

      CREATE TABLE users
      (
          id         BIGINT                              NOT NULL COMMENT '用户ID（雪花ID）'
              PRIMARY KEY,
          username   VARCHAR(50)                         NOT NULL COMMENT '用户名',
          avatar_url VARCHAR(500)                        NOT NULL COMMENT '用户头像URL',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          CONSTRAINT username
              UNIQUE (username)
      )
          COMMENT '用户表';





