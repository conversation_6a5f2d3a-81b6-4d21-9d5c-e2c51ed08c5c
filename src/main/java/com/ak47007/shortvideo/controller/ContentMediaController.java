package com.ak47007.shortvideo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ak47007.shortvideo.common.Result;
import com.ak47007.shortvideo.entity.ContentMedia;
import com.ak47007.shortvideo.service.ContentMediaService;
import com.ak47007.shortvideo.service.ContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Content Media Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/content-media")
@Validated
public class ContentMediaController {

    @Autowired
    private ContentMediaService contentMediaService;

    @Autowired
    private ContentService contentService;

    /**
     * 创建内容媒体文件
     */
    @PostMapping
    public Result<ContentMedia> createContentMedia(@Valid @RequestBody ContentMedia contentMedia) {
        // 检查内容是否存在
        if (contentService.getById(contentMedia.getContentId()) == null) {
            return Result.error("内容不存在");
        }

        boolean success = contentMediaService.save(contentMedia);
        if (success) {
            return Result.success("媒体文件创建成功", contentMedia);
        } else {
            return Result.error("媒体文件创建失败");
        }
    }

    /**
     * 根据ID查询内容媒体文件
     */
    @GetMapping("/{id}")
    public Result<ContentMedia> getContentMediaById(@PathVariable @NotNull Long id) {
        ContentMedia contentMedia = contentMediaService.getById(id);
        if (contentMedia != null) {
            return Result.success(contentMedia);
        } else {
            return Result.error("媒体文件不存在");
        }
    }

    /**
     * 分页查询内容媒体文件
     */
    @GetMapping
    public Result<IPage<ContentMedia>> pageContentMedia(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long contentId) {
        Page<ContentMedia> page = new Page<>(current, size);
        IPage<ContentMedia> result = contentMediaService.pageContentMedia(page, contentId);
        return Result.success(result);
    }

    /**
     * 查询所有内容媒体文件
     */
    @GetMapping("/all")
    public Result<List<ContentMedia>> getAllContentMedia() {
        List<ContentMedia> contentMediaList = contentMediaService.list();
        return Result.success(contentMediaList);
    }

    /**
     * 根据内容ID查询媒体文件
     */
    @GetMapping("/content/{contentId}")
    public Result<List<ContentMedia>> getContentMediaByContentId(@PathVariable @NotNull Long contentId) {
        // 检查内容是否存在
        if (contentService.getById(contentId) == null) {
            return Result.error("内容不存在");
        }

        List<ContentMedia> contentMediaList = contentMediaService.getByContentId(contentId);
        return Result.success(contentMediaList);
    }

    /**
     * 更新内容媒体文件
     */
    @PutMapping("/{id}")
    public Result<ContentMedia> updateContentMedia(@PathVariable @NotNull Long id, @Valid @RequestBody ContentMedia contentMedia) {
        ContentMedia existingContentMedia = contentMediaService.getById(id);
        if (existingContentMedia == null) {
            return Result.error("媒体文件不存在");
        }

        // 检查内容是否存在
        if (contentService.getById(contentMedia.getContentId()) == null) {
            return Result.error("内容不存在");
        }

        contentMedia.setId(id);
        boolean success = contentMediaService.updateById(contentMedia);
        if (success) {
            return Result.success("媒体文件更新成功", contentMedia);
        } else {
            return Result.error("媒体文件更新失败");
        }
    }

    /**
     * 删除内容媒体文件
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteContentMedia(@PathVariable @NotNull Long id) {
        ContentMedia contentMedia = contentMediaService.getById(id);
        if (contentMedia == null) {
            return Result.error("媒体文件不存在");
        }

        boolean success = contentMediaService.removeById(id);
        if (success) {
            return Result.success("媒体文件删除成功");
        } else {
            return Result.error("媒体文件删除失败");
        }
    }

    /**
     * 根据内容ID删除所有媒体文件
     */
    @DeleteMapping("/content/{contentId}")
    public Result<String> deleteContentMediaByContentId(@PathVariable @NotNull Long contentId) {
        // 检查内容是否存在
        if (contentService.getById(contentId) == null) {
            return Result.error("内容不存在");
        }

        boolean success = contentMediaService.deleteByContentId(contentId);
        if (success) {
            return Result.success("媒体文件删除成功");
        } else {
            return Result.error("媒体文件删除失败");
        }
    }
}
