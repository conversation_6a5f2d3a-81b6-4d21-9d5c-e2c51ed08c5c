package com.ak47007.shortvideo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ak47007.shortvideo.entity.ContentMedia;
import com.ak47007.shortvideo.mapper.ContentMediaMapper;
import com.ak47007.shortvideo.service.ContentMediaService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Content Media Service Implementation
 *
 * <AUTHOR>
 */
@Service
public class ContentMediaServiceImpl extends ServiceImpl<ContentMediaMapper, ContentMedia> implements ContentMediaService {

    @Override
    public IPage<ContentMedia> pageContentMedia(Page<ContentMedia> page, Long contentId) {
        LambdaQueryWrapper<ContentMedia> queryWrapper = new LambdaQueryWrapper<>();
        if (contentId != null) {
            queryWrapper.eq(ContentMedia::getContentId, contentId);
        }
        queryWrapper.orderByAsc(ContentMedia::getMediaOrder);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<ContentMedia> getByContentId(Long contentId) {
        LambdaQueryWrapper<ContentMedia> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentMedia::getContentId, contentId);
        queryWrapper.orderByAsc(ContentMedia::getMediaOrder);
        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteByContentId(Long contentId) {
        LambdaQueryWrapper<ContentMedia> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentMedia::getContentId, contentId);
        return this.remove(queryWrapper);
    }
}
