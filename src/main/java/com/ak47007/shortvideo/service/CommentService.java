package com.ak47007.shortvideo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ak47007.shortvideo.entity.Comment;

import java.util.List;

/**
 * Comment Service
 *
 * <AUTHOR>
 */
public interface CommentService extends IService<Comment> {

    /**
     * 分页查询评论
     *
     * @param page      分页参数
     * @param contentId 内容ID（可选）
     * @param userId    用户ID（可选）
     * @param parentId  父评论ID（可选）
     * @return 分页结果
     */
    IPage<Comment> pageComments(Page<Comment> page, Long contentId, Long userId, Long parentId);

    /**
     * 根据内容ID查询评论列表
     *
     * @param contentId 内容ID
     * @return 评论列表
     */
    List<Comment> getByContentId(Long contentId);

    /**
     * 根据用户ID查询评论列表
     *
     * @param userId 用户ID
     * @return 评论列表
     */
    List<Comment> getByUserId(Long userId);

    /**
     * 根据父评论ID查询子评论列表
     *
     * @param parentId 父评论ID
     * @return 子评论列表
     */
    List<Comment> getByParentId(Long parentId);

    /**
     * 根据内容ID删除评论
     *
     * @param contentId 内容ID
     * @return 是否删除成功
     */
    boolean deleteByContentId(Long contentId);
}
